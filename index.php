<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AWS RDS Database Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AWS RDS Database Connection Test</h1>
        
        <?php
        // Database configuration
        $host = 'dev.cl442a8c0uhp.us-east-1.rds.amazonaws.com';
        $username = 'rakesh';
        $password = 'Tp7SqeDXa9VHfsvFnC4R25';
        $database = 'franchise_new';
        $port = 3306;
        
        // Display connection info
        echo '<div class="info">';
        echo '<strong>Connection Details:</strong><br>';
        echo 'Host: ' . htmlspecialchars($host) . '<br>';
        echo 'Username: ' . htmlspecialchars($username) . '<br>';
        echo 'Database: ' . htmlspecialchars($database) . '<br>';
        echo 'Port: ' . $port;
        echo '</div>';
        
        try {
            // Create connection
            $mysqli = new mysqli($host, $username, $password, $database, $port);
            
            // Check connection
            if ($mysqli->connect_error) {
                throw new Exception("Connection failed: " . $mysqli->connect_error);
            }
            
            echo '<div class="status success">✓ Successfully connected to AWS RDS database!</div>';
            
            // Execute the test query
            $query = "SELECT COUNT(*) as user_count FROM franchise_new.user_lead_details";
            $result = $mysqli->query($query);
            
            if ($result) {
                echo '<h2>Query Results</h2>';
                echo '<p><strong>Query:</strong> <code>' . htmlspecialchars($query) . '</code></p>';
                
                echo '<table>';
                echo '<thead>';
                echo '<tr><th>Column</th><th>Value</th></tr>';
                echo '</thead>';
                echo '<tbody>';
                
                if ($row = $result->fetch_assoc()) {
                    foreach ($row as $column => $value) {
                        echo '<tr>';
                        echo '<td>' . htmlspecialchars($column) . '</td>';
                        echo '<td>' . htmlspecialchars($value) . '</td>';
                        echo '</tr>';
                    }
                } else {
                    echo '<tr><td colspan="2">No results found</td></tr>';
                }
                
                echo '</tbody>';
                echo '</table>';
                
                $result->free();
            } else {
                throw new Exception("Query failed: " . $mysqli->error);
            }
            
            // Close connection
            $mysqli->close();
            
        } catch (Exception $e) {
            echo '<div class="status error">✗ Database connection failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
            
            // Additional debugging information
            echo '<div class="info">';
            echo '<strong>Troubleshooting Tips:</strong><br>';
            echo '1. Verify that the RDS instance is publicly accessible<br>';
            echo '2. Check security group settings to allow connections from your IP<br>';
            echo '3. Ensure the database credentials are correct<br>';
            echo '4. Verify the database name exists<br>';
            echo '5. Check if the RDS instance is running';
            echo '</div>';
        }
        ?>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666;">
            <small>LAMP Stack Database Connection Test - <?php echo date('Y-m-d H:i:s'); ?></small>
        </div>
    </div>
</body>
</html>
